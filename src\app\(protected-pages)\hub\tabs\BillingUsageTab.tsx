'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Progress from '@/components/ui/Progress'
import {
    PiCreditCardDuotone,
    PiDownloadDuotone,
    PiPencilDuotone,
    PiUsersDuotone,
    PiStackDuotone,
    PiPuzzlePieceDuotone,
    PiCloudArrowUpDuotone
} from 'react-icons/pi'

const BillingUsageTab = () => {
    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                    {/* Current Usage */}
                    <Card className="mb-4">
                        <h5 className="font-bold text-lg mb-4">Current Usage</h5>

                        <div className="space-y-4">
                            <div>
                                <div className="flex items-center mb-2">
                                    <PiUsersDuotone className="text-blue-500 text-xl mr-2" />
                                    <span className="font-medium">Users</span>
                                </div>
                                <div className="flex justify-between text-sm mb-1">
                                    <span>3 of 5 seats used</span>
                                    <span>60%</span>
                                </div>
                                <Progress percent={60} customColorClass="bg-blue-500" />
                            </div>

                            <div>
                                <div className="flex items-center mb-2">
                                    <PiStackDuotone className="text-emerald-500 text-xl mr-2" />
                                    <span className="font-medium">Frameworks</span>
                                </div>
                                <div className="flex justify-between text-sm mb-1">
                                    <span>3 of 5 frameworks activated</span>
                                    <span>60%</span>
                                </div>
                                <Progress percent={60} customColorClass="bg-emerald-500" />
                            </div>

                            <div>
                                <div className="flex items-center mb-2">
                                    <PiPuzzlePieceDuotone className="text-purple-500 text-xl mr-2" />
                                    <span className="font-medium">Add-ons</span>
                                </div>
                                <div className="flex justify-between text-sm mb-1">
                                    <span>2 add-ons activated</span>
                                    <span>-</span>
                                </div>
                                <Progress percent={100} customColorClass="bg-purple-500" />
                            </div>

                            <div>
                                <div className="flex items-center mb-2">
                                    <PiCloudArrowUpDuotone className="text-amber-500 text-xl mr-2" />
                                    <span className="font-medium">Storage</span>
                                </div>
                                <div className="flex justify-between text-sm mb-1">
                                    <span>1.2 GB of 5 GB used</span>
                                    <span>24%</span>
                                </div>
                                <Progress percent={24} customColorClass="bg-amber-500" />
                            </div>
                        </div>
                    </Card>

                    {/* Invoice History */}
                    <Card>
                        <h5 className="font-bold text-lg mb-4">Invoice History</h5>

                        <div className="overflow-x-auto">
                            <table className="min-w-full">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="text-left py-3 px-4 font-semibold">Invoice</th>
                                        <th className="text-left py-3 px-4 font-semibold">Date</th>
                                        <th className="text-left py-3 px-4 font-semibold">Amount</th>
                                        <th className="text-left py-3 px-4 font-semibold">Status</th>
                                        <th className="text-right py-3 px-4 font-semibold">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {invoices.map((invoice) => (
                                        <tr
                                            key={invoice.id}
                                            className="border-b border-gray-200 dark:border-gray-700"
                                        >
                                            <td className="py-3 px-4">
                                                {invoice.id}
                                            </td>
                                            <td className="py-3 px-4">
                                                {invoice.date}
                                            </td>
                                            <td className="py-3 px-4 font-medium">
                                                {invoice.amount}
                                            </td>
                                            <td className="py-3 px-4">
                                                <StatusBadge
                                                    className={
                                                        invoice.status === 'Paid'
                                                            ? 'bg-emerald-100 text-emerald-600 border border-emerald-200'
                                                            : 'bg-amber-100 text-amber-600 border border-amber-200'
                                                    }
                                                >
                                                    {invoice.status}
                                                </StatusBadge>
                                            </td>
                                            <td className="py-3 px-4 text-right">
                                                <Button
                                                    size="xs"
                                                    variant="default"
                                                    icon={<PiDownloadDuotone />}
                                                >
                                                    PDF
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>

                {/* Payment Method & Plan Change Log */}
                <div className="space-y-4">
                    <Card>
                        <h5 className="font-bold text-lg mb-4">Payment Method</h5>

                        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
                            <div className="flex items-start">
                                <PiCreditCardDuotone className="text-2xl text-gray-500 mr-3 mt-1" />
                                <div>
                                    <div className="font-medium">Visa ending in 4242</div>
                                    <div className="text-sm text-gray-500">Expires 12/2025</div>
                                </div>
                            </div>
                        </div>

                        <Button
                            size="sm"
                            variant="default"
                            icon={<PiPencilDuotone />}
                        >
                            Update Payment Method
                        </Button>
                    </Card>

                    <Card>
                        <h5 className="font-bold text-lg mb-4">Plan Change Log</h5>

                        <div className="space-y-4">
                            {planChanges.map((change, index) => (
                                <div
                                    key={index}
                                    className={`pb-4 ${index !== planChanges.length - 1 ? 'border-b border-gray-200 dark:border-gray-700' : ''}`}
                                >
                                    <div className="flex justify-between mb-1">
                                        <span className="font-medium">{change.action}</span>
                                        <StatusBadge
                                            className={
                                                change.type === 'upgrade'
                                                    ? 'bg-emerald-100 text-emerald-600 border border-emerald-200'
                                                    : change.type === 'downgrade'
                                                    ? 'bg-amber-100 text-amber-600 border border-amber-200'
                                                    : 'bg-blue-100 text-blue-600 border border-blue-200'
                                            }
                                        >
                                            {change.type}
                                        </StatusBadge>
                                    </div>
                                    <div className="text-sm text-gray-500 mb-1">{change.date}</div>
                                    <div className="text-sm">{change.description}</div>
                                </div>
                            ))}
                        </div>
                    </Card>
                </div>
            </div>
        </div>
    )
}

// Sample data
const invoices = [
    {
        id: 'INV-2023-12',
        date: 'Dec 15, 2023',
        amount: '£199.00',
        status: 'Paid'
    },
    {
        id: 'INV-2023-11',
        date: 'Nov 15, 2023',
        amount: '£199.00',
        status: 'Paid'
    },
    {
        id: 'INV-2023-10',
        date: 'Oct 15, 2023',
        amount: '£199.00',
        status: 'Paid'
    },
    {
        id: 'INV-2023-09',
        date: 'Sep 15, 2023',
        amount: '£49.00',
        status: 'Paid'
    },
    {
        id: 'INV-2023-08',
        date: 'Aug 15, 2023',
        amount: '£49.00',
        status: 'Paid'
    }
]

const planChanges = [
    {
        action: 'Upgraded to Growth Plan',
        type: 'upgrade',
        date: 'Sep 15, 2023',
        description: 'Upgraded from Starter (L1) to Growth (L3) plan.'
    },
    {
        action: 'Added Risk Management Add-on',
        type: 'addon',
        date: 'Oct 5, 2023',
        description: 'Added Risk Management add-on (included in Growth plan).'
    },
    {
        action: 'Added Training LMS Add-on',
        type: 'addon',
        date: 'Nov 10, 2023',
        description: 'Added Training LMS add-on (£25/mo).'
    },
    {
        action: 'Subscription Started',
        type: 'new',
        date: 'Aug 15, 2023',
        description: 'Started with Starter (L1) plan.'
    }
]

export default BillingUsageTab
