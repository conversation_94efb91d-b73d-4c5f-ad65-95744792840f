import authRoute from './authRoute'
import type { Routes } from '@/@types/routes'

export const protectedRoutes: Routes = {
    '/hub': {
        key: 'hub',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/frameworks': {
        key: 'frameworks',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Dashboard routes
    '/dashboards/ecommerce': {
        key: 'dashboards.ecommerce',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/dashboards/project': {
        key: 'dashboards.project',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/dashboards/marketing': {
        key: 'dashboards.marketing',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/dashboards/analytic': {
        key: 'dashboards.analytic',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Concepts routes
    '/concepts': {
        key: 'concepts',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Account routes
    '/concepts/account': {
        key: 'concepts.account',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/account/activity-log': {
        key: 'concepts.account.activityLog',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/account/pricing': {
        key: 'concepts.account.pricing',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/account/roles-permissions': {
        key: 'concepts.account.rolesPermissions',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/account/settings': {
        key: 'concepts.account.settings',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // AI routes
    '/concepts/ai': {
        key: 'concepts.ai',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/ai/chat': {
        key: 'concepts.ai.chat',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/ai/image': {
        key: 'concepts.ai.image',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Calendar route
    '/concepts/calendar': {
        key: 'concepts.calendar',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Chat route
    '/concepts/chat': {
        key: 'concepts.chat',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Customers routes
    '/concepts/customers': {
        key: 'concepts.customers',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/customers/customer-create': {
        key: 'concepts.customers.customerCreate',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/customers/customer-details': {
        key: 'concepts.customers.customerDetails',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/customers/customer-edit': {
        key: 'concepts.customers.customerEdit',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/customers/customer-list': {
        key: 'concepts.customers.customerList',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Dashboards routes
    '/concepts/dashboards': {
        key: 'concepts.dashboards',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/dashboards/analytic': {
        key: 'concepts.dashboards.analytic',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/dashboards/ecommerce': {
        key: 'concepts.dashboards.ecommerce',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/dashboards/marketing': {
        key: 'concepts.dashboards.marketing',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/dashboards/project': {
        key: 'concepts.dashboards.project',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // File Manager route
    '/concepts/file-manager': {
        key: 'concepts.fileManager',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Help Center routes
    '/concepts/help-center': {
        key: 'concepts.helpCenter',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/help-center/article': {
        key: 'concepts.helpCenter.article',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/help-center/edit-article': {
        key: 'concepts.helpCenter.editArticle',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/help-center/manage-article': {
        key: 'concepts.helpCenter.manageArticle',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/help-center/support-hub': {
        key: 'concepts.helpCenter.supportHub',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Mail route
    '/concepts/mail': {
        key: 'concepts.mail',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Orders routes
    '/concepts/orders': {
        key: 'concepts.orders',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/orders/order-create': {
        key: 'concepts.orders.orderCreate',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/orders/order-details': {
        key: 'concepts.orders.orderDetails',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/orders/order-edit': {
        key: 'concepts.orders.orderEdit',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/orders/order-list': {
        key: 'concepts.orders.orderList',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Products routes
    '/concepts/products': {
        key: 'concepts.products',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/products/product-create': {
        key: 'concepts.products.productCreate',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/products/product-edit': {
        key: 'concepts.products.productEdit',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/products/product-list': {
        key: 'concepts.products.productList',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },

    // Projects routes
    '/concepts/projects': {
        key: 'concepts.projects',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/projects/project-details': {
        key: 'concepts.projects.projectDetails',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/projects/project-list': {
        key: 'concepts.projects.projectList',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/projects/scrum-board': {
        key: 'concepts.projects.scrumBoard',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/concepts/projects/tasks': {
        key: 'concepts.projects.tasks',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
}

export const publicRoutes: Routes = {
    '/': {
        key: 'home',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/landing': {
        key: 'landing',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
}

export const authRoutes = authRoute
