'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Progress from '@/components/ui/Progress'
import { 
    PiArrowLeftDuotone, 
    PiShieldCheckDuotone, 
    PiListChecksDuotone,
    PiChartLineUpDuotone,
    PiWarningDuotone,
    PiClockDuotone
} from 'react-icons/pi'
import { useRouter } from 'next/navigation'

const PCIDSSPage = () => {
    const router = useRouter()

    return (
        <div className="p-6">
            {/* Header with Back Button */}
            <div className="mb-6">
                <Button 
                    variant="plain" 
                    icon={<PiArrowLeftDuotone />}
                    className="mb-4"
                    onClick={() => router.back()}
                >
                    Back to Frameworks
                </Button>
                <div className="flex items-center mb-2">
                    <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-4">
                        <PiShieldCheckDuotone className="text-2xl text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold">PCI DSS 4.0.1</h1>
                        <p className="text-gray-500 dark:text-gray-400">
                            Payment Card Industry Data Security Standard
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 text-sm rounded-md border border-emerald-200">
                        Active
                    </span>
                    <span className="text-sm text-gray-500">Global Standard</span>
                </div>
            </div>

            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/30 mr-4">
                            <PiListChecksDuotone className="text-2xl text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">264</div>
                            <div className="text-sm text-gray-500">Total Controls</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 mr-4">
                            <PiChartLineUpDuotone className="text-2xl text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">62%</div>
                            <div className="text-sm text-gray-500">Completion</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-amber-100 dark:bg-amber-900/30 mr-4">
                            <PiClockDuotone className="text-2xl text-amber-600 dark:text-amber-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">12</div>
                            <div className="text-sm text-gray-500">Pending Tasks</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-red-100 dark:bg-red-900/30 mr-4">
                            <PiWarningDuotone className="text-2xl text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">3</div>
                            <div className="text-sm text-gray-500">High Priority</div>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Progress Overview */}
                <Card className="lg:col-span-2">
                    <h3 className="text-lg font-semibold mb-4">Implementation Progress</h3>
                    <div className="mb-4">
                        <div className="flex justify-between text-sm mb-2">
                            <span>Overall Progress</span>
                            <span>164 of 264 controls completed</span>
                        </div>
                        <Progress percent={62} size="lg" />
                    </div>
                    
                    <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span className="font-medium">Requirement 1: Install and maintain network security controls</span>
                            <span className="text-sm text-emerald-600 dark:text-emerald-400">Complete</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span className="font-medium">Requirement 2: Apply secure configurations</span>
                            <span className="text-sm text-emerald-600 dark:text-emerald-400">Complete</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                            <span className="font-medium">Requirement 3: Protect stored cardholder data</span>
                            <span className="text-sm text-amber-600 dark:text-amber-400">In Progress</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span className="font-medium">Requirement 4: Protect cardholder data with strong cryptography</span>
                            <span className="text-sm text-gray-500">Not Started</span>
                        </div>
                    </div>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                        <Button
                            block
                            variant="solid"
                            className="bg-blue-500 hover:bg-blue-600"
                        >
                            View All Controls
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="border border-gray-200 dark:border-gray-600"
                        >
                            Generate Report
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="border border-gray-200 dark:border-gray-600"
                        >
                            Schedule Assessment
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="border border-gray-200 dark:border-gray-600"
                        >
                            Export Evidence
                        </Button>
                    </div>
                </Card>
            </div>

            {/* Coming Soon Notice */}
            <Card className="mt-6 border-2 border-dashed border-blue-300 dark:border-blue-600">
                <div className="text-center py-8">
                    <PiShieldCheckDuotone className="text-4xl text-blue-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">PCI DSS Module Coming Soon</h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                        We're building comprehensive PCI DSS compliance management features including:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                        <div>• Detailed control management</div>
                        <div>• Evidence collection</div>
                        <div>• Risk assessment tools</div>
                        <div>• Automated compliance tracking</div>
                        <div>• Audit preparation</div>
                        <div>• Reporting and analytics</div>
                    </div>
                    <Button variant="solid" className="bg-blue-500 hover:bg-blue-600">
                        Get Notified When Ready
                    </Button>
                </div>
            </Card>
        </div>
    )
}

export default PCIDSSPage
