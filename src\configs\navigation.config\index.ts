import dashboardsNavigationConfig from './dashboards.navigation.config'
// import conceptsNavigationConfig from './concepts.navigation.config'
import settingsNavigationConfig from './settings.navigation.config'
// import uiComponentNavigationConfig from './ui-components.navigation.config'
// import authNavigationConfig from './auth.navigation.config'
// import othersNavigationConfig from './others.navigation.config'
// import guideNavigationConfig from './guide.navigation.config'

import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    ...dashboardsNavigationConfig,
    // ...conceptsNavigationConfig,
    ...settingsNavigationConfig,
    // ...uiComponentNavigationConfig,
    // ...authNavigationConfig,
    // ...othersNavigationConfig,
    // ...guideNavigationConfig,
]

export default navigationConfig








// import {
//     NAV_ITEM_TYPE_ITEM,
//     NAV_ITEM_TYPE_COLLAPSE,
// } from '@/constants/navigation.constant'

// import type { NavigationTree } from '@/@types/navigation'

// const navigationConfig: NavigationTree[] = [
//     {
//         key: 'hub',
//         path: '/hub',
//         title: 'HUB',
//         translateKey: 'nav.hub',
//         icon: 'hub',
//         type: NAV_ITEM_TYPE_ITEM,
//         authority: [],
//         subMenu: [],
//     },
//     {
//         key: 'frameworks',
//         path: '/frameworks',
//         title: 'Frameworks',
//         translateKey: 'nav.frameworks',
//         icon: 'frameworks',
//         type: NAV_ITEM_TYPE_ITEM,
//         authority: [],
//         subMenu: [],
//     },
//     {
//         key: 'concepts',
//         path: '/concepts',
//         title: 'Concepts',
//         translateKey: 'nav.concepts',
//         icon: 'concepts',
//         type: NAV_ITEM_TYPE_COLLAPSE,
//         authority: [],
//         subMenu: [
//             {
//                 key: 'concepts.account',
//                 path: '/concepts/account',
//                 title: 'Account',
//                 translateKey: 'nav.concepts.account',
//                 icon: 'account',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.account.activityLog',
//                         path: '/concepts/account/activity-log',
//                         title: 'Activity Log',
//                         translateKey: 'nav.concepts.account.activityLog',
//                         icon: 'activityLog',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.account.pricing',
//                         path: '/concepts/account/pricing',
//                         title: 'Pricing',
//                         translateKey: 'nav.concepts.account.pricing',
//                         icon: 'pricing',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.account.rolesPermissions',
//                         path: '/concepts/account/roles-permissions',
//                         title: 'Roles & Permissions',
//                         translateKey: 'nav.concepts.account.rolesPermissions',
//                         icon: 'rolesPermissions',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.account.settings',
//                         path: '/concepts/account/settings',
//                         title: 'Settings',
//                         translateKey: 'nav.concepts.account.settings',
//                         icon: 'settings',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.ai',
//                 path: '/concepts/ai',
//                 title: 'AI',
//                 translateKey: 'nav.concepts.ai',
//                 icon: 'ai',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.ai.chat',
//                         path: '/concepts/ai/chat',
//                         title: 'AI Chat',
//                         translateKey: 'nav.concepts.ai.chat',
//                         icon: 'aiChat',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.ai.image',
//                         path: '/concepts/ai/image',
//                         title: 'AI Image',
//                         translateKey: 'nav.concepts.ai.image',
//                         icon: 'aiImage',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.calendar',
//                 path: '/concepts/calendar',
//                 title: 'Calendar',
//                 translateKey: 'nav.concepts.calendar',
//                 icon: 'calendar',
//                 type: NAV_ITEM_TYPE_ITEM,
//                 authority: [],
//                 subMenu: [],
//             },
//             {
//                 key: 'concepts.chat',
//                 path: '/concepts/chat',
//                 title: 'Chat',
//                 translateKey: 'nav.concepts.chat',
//                 icon: 'chat',
//                 type: NAV_ITEM_TYPE_ITEM,
//                 authority: [],
//                 subMenu: [],
//             },
//             {
//                 key: 'concepts.customers',
//                 path: '/concepts/customers',
//                 title: 'Customers',
//                 translateKey: 'nav.concepts.customers',
//                 icon: 'customers',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.customers.customerCreate',
//                         path: '/concepts/customers/customer-create',
//                         title: 'Create Customer',
//                         translateKey: 'nav.concepts.customers.customerCreate',
//                         icon: 'customerCreate',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.customers.customerDetails',
//                         path: '/concepts/customers/customer-details',
//                         title: 'Customer Details',
//                         translateKey: 'nav.concepts.customers.customerDetails',
//                         icon: 'customerDetails',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.customers.customerEdit',
//                         path: '/concepts/customers/customer-edit',
//                         title: 'Edit Customer',
//                         translateKey: 'nav.concepts.customers.customerEdit',
//                         icon: 'customerEdit',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.customers.customerList',
//                         path: '/concepts/customers/customer-list',
//                         title: 'Customer List',
//                         translateKey: 'nav.concepts.customers.customerList',
//                         icon: 'customerList',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.dashboards',
//                 path: '/concepts/dashboards',
//                 title: 'Dashboards',
//                 translateKey: 'nav.concepts.dashboards',
//                 icon: 'dashboards',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.dashboards.analytic',
//                         path: '/concepts/dashboards/analytic',
//                         title: 'Analytic',
//                         translateKey: 'nav.concepts.dashboards.analytic',
//                         icon: 'analytic',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.dashboards.ecommerce',
//                         path: '/concepts/dashboards/ecommerce',
//                         title: 'Ecommerce',
//                         translateKey: 'nav.concepts.dashboards.ecommerce',
//                         icon: 'ecommerce',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.dashboards.marketing',
//                         path: '/concepts/dashboards/marketing',
//                         title: 'Marketing',
//                         translateKey: 'nav.concepts.dashboards.marketing',
//                         icon: 'marketing',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.dashboards.project',
//                         path: '/concepts/dashboards/project',
//                         title: 'Project',
//                         translateKey: 'nav.concepts.dashboards.project',
//                         icon: 'project',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.fileManager',
//                 path: '/concepts/file-manager',
//                 title: 'File Manager',
//                 translateKey: 'nav.concepts.fileManager',
//                 icon: 'fileManager',
//                 type: NAV_ITEM_TYPE_ITEM,
//                 authority: [],
//                 subMenu: [],
//             },
//             {
//                 key: 'concepts.helpCenter',
//                 path: '/concepts/help-center',
//                 title: 'Help Center',
//                 translateKey: 'nav.concepts.helpCenter',
//                 icon: 'helpCenter',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.helpCenter.article',
//                         path: '/concepts/help-center/article',
//                         title: 'Article',
//                         translateKey: 'nav.concepts.helpCenter.article',
//                         icon: 'article',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.helpCenter.editArticle',
//                         path: '/concepts/help-center/edit-article',
//                         title: 'Edit Article',
//                         translateKey: 'nav.concepts.helpCenter.editArticle',
//                         icon: 'editArticle',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.helpCenter.manageArticle',
//                         path: '/concepts/help-center/manage-article',
//                         title: 'Manage Article',
//                         translateKey: 'nav.concepts.helpCenter.manageArticle',
//                         icon: 'manageArticle',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.helpCenter.supportHub',
//                         path: '/concepts/help-center/support-hub',
//                         title: 'Support Hub',
//                         translateKey: 'nav.concepts.helpCenter.supportHub',
//                         icon: 'supportHub',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.mail',
//                 path: '/concepts/mail',
//                 title: 'Mail',
//                 translateKey: 'nav.concepts.mail',
//                 icon: 'mail',
//                 type: NAV_ITEM_TYPE_ITEM,
//                 authority: [],
//                 subMenu: [],
//             },
//             {
//                 key: 'concepts.orders',
//                 path: '/concepts/orders',
//                 title: 'Orders',
//                 translateKey: 'nav.concepts.orders',
//                 icon: 'orders',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.orders.orderCreate',
//                         path: '/concepts/orders/order-create',
//                         title: 'Create Order',
//                         translateKey: 'nav.concepts.orders.orderCreate',
//                         icon: 'orderCreate',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.orders.orderDetails',
//                         path: '/concepts/orders/order-details',
//                         title: 'Order Details',
//                         translateKey: 'nav.concepts.orders.orderDetails',
//                         icon: 'orderDetails',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.orders.orderEdit',
//                         path: '/concepts/orders/order-edit',
//                         title: 'Edit Order',
//                         translateKey: 'nav.concepts.orders.orderEdit',
//                         icon: 'orderEdit',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.orders.orderList',
//                         path: '/concepts/orders/order-list',
//                         title: 'Order List',
//                         translateKey: 'nav.concepts.orders.orderList',
//                         icon: 'orderList',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.products',
//                 path: '/concepts/products',
//                 title: 'Products',
//                 translateKey: 'nav.concepts.products',
//                 icon: 'products',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.products.productCreate',
//                         path: '/concepts/products/product-create',
//                         title: 'Create Product',
//                         translateKey: 'nav.concepts.products.productCreate',
//                         icon: 'productCreate',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.products.productEdit',
//                         path: '/concepts/products/product-edit',
//                         title: 'Edit Product',
//                         translateKey: 'nav.concepts.products.productEdit',
//                         icon: 'productEdit',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.products.productList',
//                         path: '/concepts/products/product-list',
//                         title: 'Product List',
//                         translateKey: 'nav.concepts.products.productList',
//                         icon: 'productList',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//             {
//                 key: 'concepts.projects',
//                 path: '/concepts/projects',
//                 title: 'Projects',
//                 translateKey: 'nav.concepts.projects',
//                 icon: 'projects',
//                 type: NAV_ITEM_TYPE_COLLAPSE,
//                 authority: [],
//                 subMenu: [
//                     {
//                         key: 'concepts.projects.projectDetails',
//                         path: '/concepts/projects/project-details',
//                         title: 'Project Details',
//                         translateKey: 'nav.concepts.projects.projectDetails',
//                         icon: 'projectDetails',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.projects.projectList',
//                         path: '/concepts/projects/project-list',
//                         title: 'Project List',
//                         translateKey: 'nav.concepts.projects.projectList',
//                         icon: 'projectList',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.projects.scrumBoard',
//                         path: '/concepts/projects/scrum-board',
//                         title: 'Scrum Board',
//                         translateKey: 'nav.concepts.projects.scrumBoard',
//                         icon: 'scrumBoard',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                     {
//                         key: 'concepts.projects.tasks',
//                         path: '/concepts/projects/tasks',
//                         title: 'Tasks',
//                         translateKey: 'nav.concepts.projects.tasks',
//                         icon: 'tasks',
//                         type: NAV_ITEM_TYPE_ITEM,
//                         authority: [],
//                         subMenu: [],
//                     },
//                 ],
//             },
//         ],
//     },
// ]

// export default navigationConfig
