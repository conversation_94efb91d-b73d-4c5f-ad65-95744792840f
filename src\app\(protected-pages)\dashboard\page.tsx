'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Progress from '@/components/ui/Progress'
import { 
    PiStackDuotone, 
    PiChartLineUpDuotone, 
    PiClockDuotone,
    PiWarningDuotone,
    PiArrowRightDuotone,
    PiPlusDuotone
} from 'react-icons/pi'

const DashboardPage = () => {
    return (
        <div className="p-6">
            {/* Welcome Section */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Welcome to CheckGap</h1>
                <p className="text-gray-600 dark:text-gray-400 text-lg">
                    Your framework-agnostic compliance operations platform
                </p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/30 mr-4">
                            <PiStackDuotone className="text-2xl text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">1</div>
                            <div className="text-sm text-gray-500">Active Framework</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 mr-4">
                            <PiChartLineUpDuotone className="text-2xl text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">62%</div>
                            <div className="text-sm text-gray-500">Compliance Score</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-amber-100 dark:bg-amber-900/30 mr-4">
                            <PiClockDuotone className="text-2xl text-amber-600 dark:text-amber-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">12</div>
                            <div className="text-sm text-gray-500">Pending Tasks</div>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div className="flex items-center">
                        <div className="p-3 rounded-lg bg-red-100 dark:bg-red-900/30 mr-4">
                            <PiWarningDuotone className="text-2xl text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                            <div className="text-2xl font-bold">3</div>
                            <div className="text-sm text-gray-500">Overdue Items</div>
                        </div>
                    </div>
                </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Quick Actions */}
                <Card className="lg:col-span-1">
                    <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                        <Button
                            block
                            variant="default"
                            className="justify-between border border-gray-200 dark:border-gray-600"
                            icon={<PiStackDuotone />}
                            suffix={<PiArrowRightDuotone />}
                        >
                            Browse Frameworks
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="justify-between border border-gray-200 dark:border-gray-600"
                            icon={<PiPlusDuotone />}
                            suffix={<PiArrowRightDuotone />}
                        >
                            Add Custom Framework
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="justify-between border border-gray-200 dark:border-gray-600"
                            icon={<PiChartLineUpDuotone />}
                            suffix={<PiArrowRightDuotone />}
                        >
                            View Reports
                        </Button>
                    </div>
                </Card>

                {/* Active Frameworks */}
                <Card className="lg:col-span-2">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold">Active Frameworks</h3>
                        <Button size="sm" variant="plain">View All</Button>
                    </div>
                    
                    <div className="space-y-4">
                        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                                <div>
                                    <h4 className="font-medium">PCI DSS 4.0.1</h4>
                                    <p className="text-sm text-gray-500">Payment Card Industry Data Security Standard</p>
                                </div>
                                <span className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 text-xs rounded-md">
                                    Active
                                </span>
                            </div>
                            <div className="mb-2">
                                <div className="flex justify-between text-sm mb-1">
                                    <span>Progress</span>
                                    <span>164 of 264 controls</span>
                                </div>
                                <Progress percent={62} size="sm" />
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Last updated: Nov 15, 2023</span>
                                <Button size="xs" variant="solid" className="bg-blue-500 hover:bg-blue-600">
                                    Open Module
                                </Button>
                            </div>
                        </div>

                        {/* Placeholder for future frameworks */}
                        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                            <PiPlusDuotone className="text-3xl text-gray-400 mx-auto mb-2" />
                            <p className="text-gray-500 mb-2">Add more frameworks</p>
                            <Button size="sm" variant="plain">Browse Framework Library</Button>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Recent Activity */}
            <Card className="mt-6">
                <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            <span className="text-sm">PCI DSS control 3.2.1 updated</span>
                        </div>
                        <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                            <span className="text-sm">Vulnerability scan completed</span>
                        </div>
                        <span className="text-xs text-gray-500">1 day ago</span>
                    </div>
                    <div className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-center">
                            <div className="w-2 h-2 bg-amber-500 rounded-full mr-3"></div>
                            <span className="text-sm">Policy review reminder sent</span>
                        </div>
                        <span className="text-xs text-gray-500">3 days ago</span>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default DashboardPage
