import {
    NAV_ITEM_TYPE_ITEM,
} from '@/constants/navigation.constant'
import type { NavigationTree } from '@/@types/navigation'

const settingsNavigationConfig: NavigationTree[] = [
    {
        key: 'settings',
        path: '/settings',
        title: 'Settings',
        translateKey: 'Settings',
        icon: 'settings',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
]

export default settingsNavigationConfig
