import {
    PiStackDuotone,
    PiCubeDuotone,
    PiUserDuotone,
    PiClockDuotone,
    PiCurrencyDollarDuotone,
    PiShieldCheckDuotone,
    PiGearDuotone,
    PiRobotDuotone,
    PiChatCircleDuotone,
    PiImageDuotone,
    PiCalendarDuotone,
    PiUsersDuotone,
    PiUserPlusDuotone,
    PiUserCircleDuotone,
    PiPencilDuotone,
    PiListDuotone,
    PiChartBarDuotone,
    PiStorefrontDuotone,
    PiMegaphoneDuotone,
    PiKanbanDuotone,
    PiFolderDuotone,
    PiQuestionDuotone,
    PiArticleDuotone,
    PiLifebuoyDuotone,
    PiEnvelopeDuotone,
    PiReceiptDuotone,
    PiPlusDuotone,
    PiEyeDuotone,
    PiPackageDuotone,
    PiProjectorScreenDuotone,
    PiCheckSquareDuotone,
    PiSquaresFourDuotone,
    PiBugDuotone,
} from 'react-icons/pi'
import type { JSX } from 'react'

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    // Main navigation icons
    dashboard: <PiSquaresFourDuotone />,
    frameworks: <PiStackDuotone />,
    settings: <PiGearDuotone />,
    concepts: <PiCubeDuotone />,

    // Framework specific icons
    pciDss: <PiShieldCheckDuotone />,

    // Account icons
    account: <PiUserDuotone />,
    activityLog: <PiClockDuotone />,
    pricing: <PiCurrencyDollarDuotone />,
    rolesPermissions: <PiShieldCheckDuotone />,

    // AI icons
    ai: <PiRobotDuotone />,
    aiChat: <PiChatCircleDuotone />,
    aiImage: <PiImageDuotone />,

    // Other concepts icons
    calendar: <PiCalendarDuotone />,
    chat: <PiChatCircleDuotone />,

    // Customers icons
    customers: <PiUsersDuotone />,
    customerCreate: <PiUserPlusDuotone />,
    customerDetails: <PiUserCircleDuotone />,
    customerEdit: <PiPencilDuotone />,
    customerList: <PiListDuotone />,

    // Dashboards icons
    dashboards: <PiChartBarDuotone />,
    analytic: <PiChartBarDuotone />,
    ecommerce: <PiStorefrontDuotone />,
    marketing: <PiMegaphoneDuotone />,
    project: <PiKanbanDuotone />,

    // File Manager
    fileManager: <PiFolderDuotone />,

    // Help Center icons
    helpCenter: <PiQuestionDuotone />,
    article: <PiArticleDuotone />,
    editArticle: <PiPencilDuotone />,
    manageArticle: <PiListDuotone />,
    supportHub: <PiLifebuoyDuotone />,

    // Mail
    mail: <PiEnvelopeDuotone />,

    // Orders icons
    orders: <PiReceiptDuotone />,
    orderCreate: <PiPlusDuotone />,
    orderDetails: <PiEyeDuotone />,
    orderEdit: <PiPencilDuotone />,
    orderList: <PiListDuotone />,

    // Products icons
    products: <PiPackageDuotone />,
    productCreate: <PiPlusDuotone />,
    productEdit: <PiPencilDuotone />,
    productList: <PiListDuotone />,

    // Projects icons
    projects: <PiProjectorScreenDuotone />,
    projectDetails: <PiEyeDuotone />,
    projectList: <PiListDuotone />,
    scrumBoard: <PiKanbanDuotone />,
    tasks: <PiCheckSquareDuotone />,

    // Dashboard icons
    dashboard: <PiSquaresFourDuotone />,
    dashboardEcommerce: <PiStorefrontDuotone />,
    dashboardProject: <PiKanbanDuotone />,
    dashboardMarketing: <PiMegaphoneDuotone />,
    dashboardAnalytic: <PiChartBarDuotone />,

    // Additional concept icons
    projectScrumBoard: <PiKanbanDuotone />,
    projectTask: <PiCheckSquareDuotone />,
    projectIssue: <PiBugDuotone />,
    accountSettings: <PiGearDuotone />,
    accountActivityLogs: <PiClockDuotone />,
    accountRoleAndPermission: <PiShieldCheckDuotone />,
    accountPricing: <PiCurrencyDollarDuotone />,
    helpCeterSupportHub: <PiLifebuoyDuotone />,
    helpCeterArticle: <PiArticleDuotone />,
    helpCeterEditArticle: <PiPencilDuotone />,
    helpCeterManageArticle: <PiListDuotone />,
    ordeDetails: <PiEyeDuotone />,
}

export default navigationIcon