'use client'

import { useState } from 'react'
import Tabs from '@/components/ui/Tabs'
import {
    PiHouseLineDuotone,
    PiUserCircleDuotone,
    PiCreditCardDuotone,
    PiGearDuotone
} from 'react-icons/pi'

// Tab components
import OverviewTab from './tabs/OverviewTab'
import AccountTab from './tabs/AccountTab'
import BillingTab from './tabs/BillingTab'

const SettingsPage = () => {
    const [activeTab, setActiveTab] = useState('overview')

    return (
        <div className="p-6">
            <div className="mb-6">
                <h4 className="text-2xl font-bold mb-2">Settings</h4>
                <p className="text-gray-500 dark:text-gray-400">
                    Manage your account, subscription, and application preferences
                </p>
            </div>

            <Tabs value={activeTab} onChange={(val) => setActiveTab(val as string)}>
                <Tabs.TabList>
                    <Tabs.TabNav value="overview" icon={<PiHouseLineDuotone />}>
                        Overview
                    </Tabs.TabNav>
                    <Tabs.TabNav value="account" icon={<PiUserCircleDuotone />}>
                        Account
                    </Tabs.TabNav>
                    <Tabs.TabNav value="billing" icon={<PiCreditCardDuotone />}>
                        Billing
                    </Tabs.TabNav>
                </Tabs.TabList>
                <div className="mt-4">
                    <Tabs.TabContent value="overview">
                        <OverviewTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="account">
                        <AccountTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="billing">
                        <BillingTab />
                    </Tabs.TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default SettingsPage
