'use client'

import { useState, useMemo, useId } from 'react'
import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { HiOutlineSearch, HiPlus, HiOutlineChevronRight, HiOutlineCog } from 'react-icons/hi'

// Framework data - framework agnostic approach
const frameworksData = [
    // Active Framework
    {
        id: 'pci-dss',
        name: 'PCI DSS 4.0.1',
        category: 'Cybersecurity',
        description: 'Payment Card Industry Data Security Standard for organizations that handle credit cards.',
        controlCount: 264,
        progress: 62,
        status: 'Active',
        region: 'Global',
        lastUpdated: '2023-11-15',
        notifications: 3,
        isAvailable: true,
    },
    // Placeholder Frameworks
    {
        id: 'iso-27001',
        name: 'ISO/IEC 27001',
        category: 'Cybersecurity',
        description: 'International standard for information security management systems.',
        controlCount: 114,
        progress: 0,
        status: 'Coming Soon',
        region: 'Global',
        lastUpdated: null,
        notifications: 0,
        isAvailable: false,
    },
    {
        id: 'dora',
        name: 'DORA',
        category: 'Financial',
        description: 'Digital Operational Resilience Act for financial services in the EU.',
        controlCount: 95,
        progress: 0,
        status: 'Coming Soon',
        region: 'EU',
        lastUpdated: null,
        notifications: 0,
        isAvailable: false,
    },
    {
        id: 'sox',
        name: 'Sarbanes-Oxley (SOX)',
        category: 'Financial',
        description: 'Regulations for financial reporting and corporate governance.',
        controlCount: 87,
        progress: 0,
        status: 'Coming Soon',
        region: 'US',
        lastUpdated: null,
        notifications: 0,
        isAvailable: false,
    },
    {
        id: 'gdpr',
        name: 'GDPR',
        category: 'Privacy',
        description: 'General Data Protection Regulation for data privacy in the EU.',
        controlCount: 99,
        progress: 0,
        status: 'Coming Soon',
        region: 'EU',
        lastUpdated: null,
        notifications: 0,
        isAvailable: false,
    },
    {
        id: 'iso-9001',
        name: 'ISO 9001',
        category: 'Quality',
        description: 'Quality management system standard for organizations.',
        controlCount: 56,
        progress: 0,
        status: 'Coming Soon',
        region: 'Global',
        lastUpdated: null,
        notifications: 0,
        isAvailable: false,
    },
]

// Status badge styles
const getStatusStyles = (status: string) => {
    switch (status) {
        case 'Active':
            return {
                wrapperClass: 'bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-500',
                textClass: 'text-emerald-700 dark:text-emerald-400'
            }
        case 'Coming Soon':
            return {
                wrapperClass: 'bg-blue-100 dark:bg-blue-900/30 border border-blue-500',
                textClass: 'text-blue-700 dark:text-blue-400'
            }
        case 'In Progress':
            return {
                wrapperClass: 'bg-amber-100 dark:bg-amber-900/30 border border-amber-500',
                textClass: 'text-amber-700 dark:text-amber-400'
            }
        case 'Not Started':
            return {
                wrapperClass: 'bg-gray-100 dark:bg-gray-700/50 border border-gray-400',
                textClass: 'text-gray-700 dark:text-gray-400'
            }
        default:
            return {
                wrapperClass: 'bg-gray-100 dark:bg-gray-700/50 border border-gray-400',
                textClass: 'text-gray-700 dark:text-gray-400'
            }
    }
}

const FrameworksPage = () => {
    // Generate stable IDs for select components to prevent hydration mismatch
    const categorySelectId = useId()
    const regionSelectId = useId()
    const statusSelectId = useId()

    const [searchTerm, setSearchTerm] = useState('')
    const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
    const [regionFilter, setRegionFilter] = useState<string | null>(null)
    const [statusFilter, setStatusFilter] = useState<string | null>(null)

    // Extract unique categories, regions, and statuses for filters
    const categories = [...new Set(frameworksData.map(f => f.category))]
    const regions = [...new Set(frameworksData.map(f => f.region))]
    const statuses = [...new Set(frameworksData.map(f => f.status))]

    // Filter frameworks based on search and filters
    const filteredFrameworks = useMemo(() => {
        return frameworksData.filter(framework => {
            const matchesSearch = searchTerm === '' ||
                framework.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                framework.description.toLowerCase().includes(searchTerm.toLowerCase())

            const matchesCategory = categoryFilter === null || framework.category === categoryFilter
            const matchesRegion = regionFilter === null || framework.region === regionFilter
            const matchesStatus = statusFilter === null || framework.status === statusFilter

            return matchesSearch && matchesCategory && matchesRegion && matchesStatus
        })
    }, [searchTerm, categoryFilter, regionFilter, statusFilter])

    return (
        <div className="p-6">
            {/* Header Section */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold mb-2">Frameworks</h1>
                <p className="text-gray-500 dark:text-gray-400">
                    Manage compliance across any framework, standard, or custom controls. CheckGap adapts to your organization's unique requirements.
                </p>
            </div>

            {/* Filters and Actions */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
                <div className="flex-grow">
                    <Input
                        prefix={<HiOutlineSearch className="text-lg" />}
                        placeholder="Search frameworks..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="flex flex-wrap gap-3">
                    <Select
                        className="min-w-[180px]"
                        placeholder="Category"
                        options={categories.map(cat => ({ value: cat, label: cat }))}
                        value={categoryFilter ? { value: categoryFilter, label: categoryFilter } : null}
                        onChange={(option) => setCategoryFilter(option?.value || null)}
                        isClearable
                        instanceId={categorySelectId}
                    />
                    <Select
                        className="min-w-[180px]"
                        placeholder="Region"
                        options={regions.map(region => ({ value: region, label: region }))}
                        value={regionFilter ? { value: regionFilter, label: regionFilter } : null}
                        onChange={(option) => setRegionFilter(option?.value || null)}
                        isClearable
                        instanceId={regionSelectId}
                    />
                    <Select
                        className="min-w-[180px]"
                        placeholder="Status"
                        options={statuses.map(status => ({ value: status, label: status }))}
                        value={statusFilter ? { value: statusFilter, label: statusFilter } : null}
                        onChange={(option) => setStatusFilter(option?.value || null)}
                        isClearable
                        instanceId={statusSelectId}
                    />
                    <Button variant="solid" icon={<HiPlus />} className="bg-emerald-500 hover:bg-emerald-600">Add Custom Framework</Button>
                </div>
            </div>

            {/* Frameworks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredFrameworks.map(framework => (
                    <Card
                        key={framework.id}
                        clickable={framework.isAvailable}
                        className={`h-full ${!framework.isAvailable ? 'opacity-75' : ''}`}
                        bodyClass="h-full flex flex-col"
                        onClick={() => {
                            if (framework.isAvailable && framework.id === 'pci-dss') {
                                window.location.href = '/frameworks/pci-dss'
                            }
                        }}
                    >
                        <div className="flex justify-between items-start mb-2">
                            <div className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusStyles(framework.status).wrapperClass}`}>
                                <span className={getStatusStyles(framework.status).textClass}>
                                    {framework.status}
                                </span>
                            </div>
                            <span className="text-xs text-gray-500">
                                {framework.region}
                            </span>
                        </div>
                        <div className="flex items-center mb-1">
                            <h4 className="font-semibold text-lg">{framework.name}</h4>
                            {framework.notifications > 0 && (
                                <div className="ml-2 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
                                    <span className="text-white text-xs font-medium">{framework.notifications}</span>
                                </div>
                            )}
                        </div>
                        <div className="text-xs inline-block mb-2 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {framework.category}
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 line-clamp-2">
                            {framework.description}
                        </p>
                        <div className="mt-auto">
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                                <span>{framework.controlCount} Controls</span>
                                {framework.isAvailable ? (
                                    <span>{framework.progress}% Complete</span>
                                ) : (
                                    <span>Available Soon</span>
                                )}
                            </div>
                            {framework.isAvailable ? (
                                <Progress percent={framework.progress} size="sm" />
                            ) : (
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div className="bg-gray-300 dark:bg-gray-600 h-2 rounded-full w-0"></div>
                                </div>
                            )}
                            <div className="flex justify-between items-center mt-4">
                                <span className="text-xs text-gray-500">
                                    {framework.lastUpdated ? `Updated: ${framework.lastUpdated}` : 'Coming Soon'}
                                </span>
                                <div className="flex gap-2">
                                    {framework.isAvailable ? (
                                        <>
                                            <Button size="xs" variant="plain" icon={<HiOutlineCog />} />
                                            <Button size="xs" variant="plain" icon={<HiOutlineChevronRight />} />
                                        </>
                                    ) : (
                                        <Button size="xs" variant="plain" disabled>
                                            Coming Soon
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </Card>
                ))}
            </div>

            {/* Empty State */}
            {filteredFrameworks.length === 0 && (
                <div className="text-center py-12">
                    <h4 className="font-semibold mb-2">No frameworks found</h4>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                        Try adjusting your search or filters to find what you need.
                    </p>
                    <Button variant="solid" icon={<HiPlus />} className="bg-emerald-500 hover:bg-emerald-600">Add Custom Framework</Button>
                </div>
            )}
        </div>
    )
}

export default FrameworksPage
