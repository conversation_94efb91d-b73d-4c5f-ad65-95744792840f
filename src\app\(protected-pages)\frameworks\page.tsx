'use client'

import { useState, useMemo, useId } from 'react'
import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { HiOutlineSearch, HiPlus, HiOutlineChevronRight, HiOutlineCog } from 'react-icons/hi'

// Sample framework data
const frameworksData = [
    {
        id: 'pci-dss',
        name: 'PCI DSS 4.0.1',
        category: 'Cybersecurity',
        description: 'Payment Card Industry Data Security Standard for organizations that handle credit cards.',
        controlCount: 264,
        progress: 62,
        status: 'Active',
        region: 'Global',
        lastUpdated: '2023-11-15',
        notifications: 3,
    },
    {
        id: 'iso-27001',
        name: 'ISO/IEC 27001',
        category: 'Cybersecurity',
        description: 'International standard for information security management systems.',
        controlCount: 114,
        progress: 78,
        status: 'Active',
        region: 'Global',
        lastUpdated: '2023-10-22',
        notifications: 0,
    },
    {
        id: 'sox',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (SOX)',
        category: 'Financial',
        description: 'Regulations for financial reporting and corporate governance.',
        controlCount: 87,
        progress: 45,
        status: 'In Progress',
        region: 'US',
        lastUpdated: '2023-12-05',
        notifications: 5,
    },
    {
        id: 'gdpr',
        name: 'GDPR',
        category: 'Privacy',
        description: 'General Data Protection Regulation for data privacy in the EU.',
        controlCount: 99,
        progress: 91,
        status: 'Active',
        region: 'EU',
        lastUpdated: '2023-09-18',
        notifications: 0,
    },
    {
        id: 'hipaa',
        name: 'HIPAA',
        category: 'Healthcare',
        description: 'Health Insurance Portability and Accountability Act for medical information privacy.',
        controlCount: 75,
        progress: 30,
        status: 'Not Started',
        region: 'US',
        lastUpdated: '2023-12-10',
        notifications: 2,
    },
    {
        id: 'iso-9001',
        name: 'ISO 9001',
        category: 'Quality',
        description: 'Quality management system standard for organizations.',
        controlCount: 56,
        progress: 85,
        status: 'Active',
        region: 'Global',
        lastUpdated: '2023-11-02',
        notifications: 0,
    },
    {
        id: 'nist-csf',
        name: 'NIST CSF',
        category: 'Cybersecurity',
        description: 'National Institute of Standards and Technology Cybersecurity Framework.',
        controlCount: 108,
        progress: 52,
        status: 'In Progress',
        region: 'US',
        lastUpdated: '2023-10-15',
        notifications: 7,
    },
    {
        id: 'iso-14001',
        name: 'ISO 14001',
        category: 'Environmental',
        description: 'Environmental management system standard.',
        controlCount: 43,
        progress: 15,
        status: 'Not Started',
        region: 'Global',
        lastUpdated: '2023-12-01',
        notifications: 1,
    },
    {
        id: 'okrs',
        name: 'OKRs',
        category: 'Strategic',
        description: 'Objectives and Key Results framework for goal setting and tracking.',
        controlCount: 32,
        progress: 68,
        status: 'Active',
        region: 'Internal',
        lastUpdated: '2023-11-28',
        notifications: 0,
    },
    {
        id: 'sdgs',
        name: 'UN SDGs',
        category: 'Environmental',
        description: 'United Nations Sustainable Development Goals.',
        controlCount: 17,
        progress: 40,
        status: 'In Progress',
        region: 'Global',
        lastUpdated: '2023-09-30',
        notifications: 4,
    },
    {
        id: 'ai-act',
        name: 'EU AI Act',
        category: 'AI Ethics',
        description: 'European Union regulations for artificial intelligence systems.',
        controlCount: 89,
        progress: 10,
        status: 'Not Started',
        region: 'EU',
        lastUpdated: '2023-12-15',
        notifications: 0,
    },
    {
        id: 'fda-21-cfr',
        name: 'FDA 21 CFR Part 11',
        category: 'Healthcare',
        description: 'FDA regulations for electronic records and signatures.',
        controlCount: 64,
        progress: 72,
        status: 'Active',
        region: 'US',
        lastUpdated: '2023-10-05',
        notifications: 2,
    },
]

// Status badge styles
const getStatusStyles = (status: string) => {
    switch (status) {
        case 'Active':
            return {
                wrapperClass: 'bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-500',
                textClass: 'text-emerald-700 dark:text-emerald-400'
            }
        case 'In Progress':
            return {
                wrapperClass: 'bg-amber-100 dark:bg-amber-900/30 border border-amber-500',
                textClass: 'text-amber-700 dark:text-amber-400'
            }
        case 'Not Started':
            return {
                wrapperClass: 'bg-gray-100 dark:bg-gray-700/50 border border-gray-400',
                textClass: 'text-gray-700 dark:text-gray-400'
            }
        case 'Archived':
            return {
                wrapperClass: 'bg-gray-100 dark:bg-gray-700/50 border border-gray-400',
                textClass: 'text-gray-700 dark:text-gray-400'
            }
        default:
            return {
                wrapperClass: 'bg-gray-100 dark:bg-gray-700/50 border border-gray-400',
                textClass: 'text-gray-700 dark:text-gray-400'
            }
    }
}

const FrameworksPage = () => {
    // Generate stable IDs for select components to prevent hydration mismatch
    const categorySelectId = useId()
    const regionSelectId = useId()
    const statusSelectId = useId()

    const [searchTerm, setSearchTerm] = useState('')
    const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
    const [regionFilter, setRegionFilter] = useState<string | null>(null)
    const [statusFilter, setStatusFilter] = useState<string | null>(null)

    // Extract unique categories, regions, and statuses for filters
    const categories = [...new Set(frameworksData.map(f => f.category))]
    const regions = [...new Set(frameworksData.map(f => f.region))]
    const statuses = [...new Set(frameworksData.map(f => f.status))]

    // Filter frameworks based on search and filters
    const filteredFrameworks = useMemo(() => {
        return frameworksData.filter(framework => {
            const matchesSearch = searchTerm === '' ||
                framework.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                framework.description.toLowerCase().includes(searchTerm.toLowerCase())

            const matchesCategory = categoryFilter === null || framework.category === categoryFilter
            const matchesRegion = regionFilter === null || framework.region === regionFilter
            const matchesStatus = statusFilter === null || framework.status === statusFilter

            return matchesSearch && matchesCategory && matchesRegion && matchesStatus
        })
    }, [searchTerm, categoryFilter, regionFilter, statusFilter])

    return (
        <div className="p-6">
            {/* Header Section */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold mb-2">Frameworks</h1>
                <p className="text-gray-500 dark:text-gray-400">
                    Browse, activate, and manage regulatory, operational, or strategic frameworks your organization needs to follow.
                </p>
            </div>

            {/* Filters and Actions */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
                <div className="flex-grow">
                    <Input
                        prefix={<HiOutlineSearch className="text-lg" />}
                        placeholder="Search frameworks..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="flex flex-wrap gap-3">
                    <Select
                        className="min-w-[180px]"
                        placeholder="Category"
                        options={categories.map(cat => ({ value: cat, label: cat }))}
                        value={categoryFilter ? { value: categoryFilter, label: categoryFilter } : null}
                        onChange={(option) => setCategoryFilter(option?.value || null)}
                        isClearable
                        instanceId={categorySelectId}
                    />
                    <Select
                        className="min-w-[180px]"
                        placeholder="Region"
                        options={regions.map(region => ({ value: region, label: region }))}
                        value={regionFilter ? { value: regionFilter, label: regionFilter } : null}
                        onChange={(option) => setRegionFilter(option?.value || null)}
                        isClearable
                        instanceId={regionSelectId}
                    />
                    <Select
                        className="min-w-[180px]"
                        placeholder="Status"
                        options={statuses.map(status => ({ value: status, label: status }))}
                        value={statusFilter ? { value: statusFilter, label: statusFilter } : null}
                        onChange={(option) => setStatusFilter(option?.value || null)}
                        isClearable
                        instanceId={statusSelectId}
                    />
                    <Button variant="solid" icon={<HiPlus />} className="bg-emerald-500 hover:bg-emerald-600">Add Custom Framework</Button>
                </div>
            </div>

            {/* Frameworks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredFrameworks.map(framework => (
                    <Card
                        key={framework.id}
                        clickable
                        className="h-full"
                        bodyClass="h-full flex flex-col"
                    >
                        <div className="flex justify-between items-start mb-2">
                            <div className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusStyles(framework.status).wrapperClass}`}>
                                <span className={getStatusStyles(framework.status).textClass}>
                                    {framework.status}
                                </span>
                            </div>
                            <span className="text-xs text-gray-500">
                                {framework.region}
                            </span>
                        </div>
                        <div className="flex items-center mb-1">
                            <h4 className="font-semibold text-lg">{framework.name}</h4>
                            {framework.notifications > 0 && (
                                <div className="ml-2 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
                                    <span className="text-white text-xs font-medium">{framework.notifications}</span>
                                </div>
                            )}
                        </div>
                        <div className="text-xs inline-block mb-2 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {framework.category}
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 line-clamp-2">
                            {framework.description}
                        </p>
                        <div className="mt-auto">
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                                <span>{framework.controlCount} Controls</span>
                                <span>{framework.progress}% Complete</span>
                            </div>
                            <Progress percent={framework.progress} size="sm" />
                            <div className="flex justify-between items-center mt-4">
                                <span className="text-xs text-gray-500">
                                    Updated: {framework.lastUpdated}
                                </span>
                                <div className="flex gap-2">
                                    <Button size="xs" variant="plain" icon={<HiOutlineCog />} />
                                    <Button size="xs" variant="plain" icon={<HiOutlineChevronRight />} />
                                </div>
                            </div>
                        </div>
                    </Card>
                ))}
            </div>

            {/* Empty State */}
            {filteredFrameworks.length === 0 && (
                <div className="text-center py-12">
                    <h4 className="font-semibold mb-2">No frameworks found</h4>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                        Try adjusting your search or filters to find what you need.
                    </p>
                    <Button variant="solid" icon={<HiPlus />} className="bg-emerald-500 hover:bg-emerald-600">Add Custom Framework</Button>
                </div>
            )}
        </div>
    )
}

export default FrameworksPage
