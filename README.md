# CheckGap

<div align="center">

  <p><strong>Framework-agnostic compliance operations platform for real-world teams</strong></p>

  [![Next.js](https://img.shields.io/badge/Next.js-15-black?style=flat&logo=next.js)](https://nextjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat&logo=typescript)](https://www.typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind-3-38B2AC?style=flat&logo=tailwind-css)](https://tailwindcss.com/)
  [![Auth.js](https://img.shields.io/badge/Auth.js-5-green?style=flat&logo=auth0)](https://authjs.dev/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## 🔍 Overview

**CheckGap** is a comprehensive compliance operations platform designed to simplify how organizations manage regulatory requirements across diverse frameworks. From cybersecurity and privacy to finance, ESG, quality, AI governance, and healthcare, CheckGap provides a unified solution for gap analysis, documentation, evidence tracking, and audit readiness.

Built for real-world compliance teams, CheckGap bridges the gap between complex regulatory requirements and practical implementation.

## ✨ Why CheckGap?

- **Framework Agnostic**: Adapt to any compliance framework or standard without being locked into a specific methodology
- **Unified Dashboard**: Manage all compliance activities in one place instead of juggling multiple tools
- **Evidence-Centric**: Built around real-world evidence collection and mapping to controls
- **Flexible Workflows**: Customize to match your organization's unique compliance processes
- **Scalable Architecture**: Grows with your organization from startup to enterprise
- **Modern Tech Stack**: Built with Next.js, TypeScript, and Tailwind CSS for performance and maintainability

## 🚀 Key Features

### Core Functionality

- **📊 Gap Analysis & Progress Tracking**
  Identify compliance gaps and track remediation progress with visual dashboards and metrics

- **📁 Policy & Document Management**
  Create, store, and manage policies with in-app editing and version control

- **📌 Evidence Collection & Mapping**
  Collect, organize, and map evidence to specific controls and requirements

- **✅ Task Management & Reminders**
  Assign tasks, set deadlines, and receive automated reminders for recurring compliance activities

- **↻ Framework Cross-Mapping**
  Map controls across multiple frameworks to reduce duplicate work

### Advanced Capabilities

- **🧹 Modular Add-ons**
  Extend functionality with specialized modules for Risk Register, Vendor Management, ESG Toolkit, and AI Governance

- **🌐 Multi-framework Support**
  Pre-built templates for PCI DSS, ISO 27001, SOX, GDPR, GRI, SDGs, ISO 9001, and more

- **🧠 AI Assistant**
  Optional AI-powered guidance for policy generation and compliance recommendations

## 🧱 Tech Stack

- **Frontend**
  - [Next.js 15](https://nextjs.org/) with App Router
  - [TypeScript](https://www.typescriptlang.org/) for type safety
  - [Tailwind CSS](https://tailwindcss.com/) for styling
  - [Framer Motion](https://www.framer.com/motion/) for animations

- **Authentication & Security**
  - [Auth.js](https://authjs.dev/) (formerly NextAuth) for authentication
  - Role-based access control
  - Secure server-side operations

- **State Management & Data Handling**
  - [Zustand](https://github.com/pmndrs/zustand) for state management
  - [React Hook Form](https://react-hook-form.com/) for form handling
  - [Zod](https://zod.dev/) for schema validation

- **Development Experience**
  - Turbopack for faster builds
  - ESLint and Prettier for code quality
  - Internationalization with RTL support

---

## 📁 Project Structure

```
/                   → Root project directory
├── public/         → Static assets
├── src/            → Source code
│   ├── @types/     → TypeScript type definitions
│   ├── app/        → Next.js App Router pages
│   │   ├── (public-pages)/landing/ → Public pages
│   │   └── layout.tsx → Root layout
│   ├── assets/     → Global styles & SVGs
│   ├── auth/       → Authentication setup
│   ├── components/ → UI components
│   │   ├── auth/           → Auth UI
│   │   ├── shared/         → Shared components
│   │   ├── template/       → Layout templates
│   │   └── ui/             → UI components
│   ├── configs/    → Configuration files
│   ├── constants/  → Constants and enums
│   ├── server/     → Server-side code and actions
│   ├── services/   → API services
│   └── utils/      → Utility functions and hooks
```

---

## 🛠️ Getting Started

### Prerequisites

- Node.js 18.17 or later
- npm or yarn

### Installation

1. **Clone the repository**

```bash
git clone https://github.com/your-org/checkgap.git
cd checkgap
```

2. **Install dependencies**

```bash
npm install
# or
yarn install
```

3. **Configure environment variables**

Create a `.env.local` file in the root directory:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# Optional OAuth providers
GITHUB_AUTH_CLIENT_ID=your_github_client_id
GITHUB_AUTH_CLIENT_SECRET=your_github_client_secret
GOOGLE_AUTH_CLIENT_ID=your_google_client_id
GOOGLE_AUTH_CLIENT_SECRET=your_google_client_secret
```

4. **Start the development server**

```bash
npm run dev
# or
yarn dev
```

Your application will be available at [http://localhost:3000](http://localhost:3000).

## 📦 Deployment

CheckGap is optimized for deployment on various platforms:

### Vercel (Recommended)

1. Push your repository to GitHub
2. Import your project to [Vercel](https://vercel.com)
3. Configure environment variables
4. Deploy

### Other Platforms

CheckGap can also be deployed on:
- [Netlify](https://netlify.com)
- [AWS Amplify](https://aws.amazon.com/amplify/)
- Docker containers (configuration included)

## 🔒 Security Features

- **Authentication**: Secure user authentication via Auth.js with multiple provider options
- **Authorization**: Role-based access control for different user types
- **Data Protection**: Server-side operations for sensitive actions
- **Audit Logging**: Built-in audit trails for compliance activities
- **Document Versioning**: Track changes to policies and documents

## 🧹 Supported Frameworks

CheckGap comes with templates for popular compliance frameworks:

| Category | Frameworks |
|----------|------------|
| **Cybersecurity** | ISO 27001, PCI DSS, NIST CSF, SOC 2, CIS Controls |
| **Privacy** | GDPR, CCPA/CPRA, HIPAA, PIPEDA |
| **Finance** | SOX, IFRS, GAAP |
| **ESG & Sustainability** | GRI, SASB, UN SDGs, TCFD |
| **Quality** | ISO 9001, Six Sigma, CMMI |
| **AI Governance** | EU AI Act, NIST AI RMF, ISO/IEC 42001 |
| **Healthcare** | HIPAA, FDA 21 CFR Part 11 |
| **Custom** | Internal controls, OKRs, Business continuity |

## 📬 Contact & Support

- **Website**: [checkgap.com](https://checkgap.com)
- **Email**: <EMAIL>
- **Documentation**: [docs.checkgap.com](https://docs.checkgap.com)
- **GitHub Issues**: For bug reports and feature requests

## 📄 License

CheckGap is available under the MIT License. See the [LICENSE](LICENSE) file for more information.
